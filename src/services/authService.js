// Сервис для работы с API авторизации

import { API_BASE_URL, API_ENDPOINTS, DEFAULT_HEADERS, REQUEST_TIMEOUT } from '@/constants/api.js'

/**
 * Выполнить HTTP запрос с обработкой ошибок
 * @param {string} url - URL для запроса
 * @param {object} options - Опции для fetch
 * @returns {Promise<object>} Ответ сервера
 */
async function makeRequest(url, options = {}) {
  const controller = new AbortController()
  const timeoutId = setTimeout(() => controller.abort(), REQUEST_TIMEOUT)

  try {
    const response = await fetch(url, {
      ...options,
      signal: controller.signal,
      headers: {
        ...DEFAULT_HEADERS,
        ...options.headers,
      },
    })

    clearTimeout(timeoutId)

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`)
    }

    return await response.json()
  } catch (error) {
    clearTimeout(timeoutId)

    if (error.name === 'AbortError') {
      throw new Error('Превышено время ожидания запроса')
    }

    throw error
  }
}

/**
 * Зарегистрировать нового пользователя
 * @param {string} nickname - Никнейм пользователя
 * @returns {Promise<string>} JWT токен
 */
export async function register(nickname) {
  if (!nickname || typeof nickname !== 'string' || nickname.trim().length === 0) {
    throw new Error('Никнейм не может быть пустым')
  }

  const trimmedNickname = nickname.trim()

  if (trimmedNickname.length < 2) {
    throw new Error('Никнейм должен содержать минимум 2 символа')
  }

  if (trimmedNickname.length > 20) {
    throw new Error('Никнейм не может быть длиннее 20 символов')
  }

  const url = `${API_BASE_URL}${API_ENDPOINTS.AUTH_REGISTER}`
  const response = await makeRequest(url, {
    method: 'POST',
    body: JSON.stringify({ nickname: trimmedNickname }),
  })

  if (!response.token || typeof response.token !== 'string') {
    throw new Error('Сервер вернул некорректный токен')
  }

  return response.token
}

/**
 * Получить профиль пользователя по JWT токену
 * @param {string} jwt - JWT токен
 * @returns {Promise<object>} Данные пользователя
 */
export async function getProfile(jwt) {
  if (!jwt || typeof jwt !== 'string') {
    throw new Error('JWT токен обязателен')
  }

  const url = `${API_BASE_URL}${API_ENDPOINTS.AUTH_PROFILE}`
  const response = await makeRequest(url, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${jwt}`,
    },
  })

  if (!response.id || !response.nickname) {
    throw new Error('Сервер вернул некорректные данные пользователя')
  }

  return {
    id: response.id,
    nickname: response.nickname,
  }
}
